<?php

namespace Modules\Dashboard\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'stats' => [
                'chatbot' => [
                    'total' => $this->resource['stats']['chatbot']['total'],
                    'active' => $this->resource['stats']['chatbot']['active'],
                    'draft' => $this->resource['stats']['chatbot']['draft'],
                    'mostUsedBot' => $this->resource['stats']['chatbot']['mostUsedBot']
                ],
                'conversation' => [
                    'total' => $this->resource['stats']['conversation']['total'],
                    'today' => $this->resource['stats']['conversation']['today'],
                    'week' => $this->resource['stats']['conversation']['week'],
                    'avgConversationLength' => round($this->resource['stats']['conversation']['avgConversationLength'], 1)
                ],
                'token' => [
                    'total' => $this->resource['stats']['token']['total'],
                    'estimatedCost' => number_format($this->resource['stats']['token']['estimatedCost'], 2)
                ],
                'knowledge' => [
                    'totalDocuments' => $this->resource['stats']['knowledge']['totalDocuments'],
                    'fileTypes' => $this->resource['stats']['knowledge']['fileTypes'],
                    'mostQueriedDoc' => $this->resource['stats']['knowledge']['mostQueriedDoc']
                ],
                'storage' => [
                    'totalUsedMB' => round($this->resource['stats']['storage']['totalUsedMB'], 1),
                    'documentsSizeMB' => round($this->resource['stats']['storage']['documentsSizeMB'], 1),
                    'attachmentsSizeMB' => round($this->resource['stats']['storage']['attachmentsSizeMB'], 1),
                    'remainingQuotaMB' => round($this->resource['stats']['storage']['remainingQuotaMB'], 1),
                    'quotaLimitMB' => $this->resource['stats']['storage']['quotaLimitMB'],
                    'usagePercent' => round(($this->resource['stats']['storage']['totalUsedMB'] / $this->resource['stats']['storage']['quotaLimitMB']) * 100, 1)
                ]
            ],
            'charts' => [
                'tokenTrend' => [
                    'labels' => $this->resource['charts']['tokenTrend']['labels'],
                    'datasets' => [
                        [
                            'label' => 'Input Tokens',
                            'data' => $this->resource['charts']['tokenTrend']['input'],
                            'borderColor' => '#3B82F6',
                            'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                            'tension' => 0.4
                        ],
                        [
                            'label' => 'Output Tokens',
                            'data' => $this->resource['charts']['tokenTrend']['output'],
                            'borderColor' => '#10B981',
                            'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                            'tension' => 0.4
                        ]
                    ]
                ],
                'conversationTrend' => [
                    'labels' => $this->resource['charts']['conversationTrend']['labels'],
                    'datasets' => [
                        [
                            'label' => 'Conversations',
                            'data' => $this->resource['charts']['conversationTrend']['values'],
                            'borderColor' => '#8B5CF6',
                            'backgroundColor' => 'rgba(139, 92, 246, 0.1)',
                            'tension' => 0.4
                        ]
                    ]
                ]
            ],
            'metadata' => [
                'lastUpdated' => now()->toISOString(),
                'timezone' => config('app.timezone'),
                'currency' => 'USD'
            ]
        ];
    }
}
