<?php

namespace Modules\Dashboard\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\Dashboard\Services\DashboardService;
use Modules\Dashboard\Http\Requests\DashboardRequest;
use Modules\Dashboard\Http\Resources\DashboardResource;

class DashboardController extends Controller
{
    use ResponseTrait;

    protected DashboardService $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('dashboard::index');
    }

    /**
     * Get dashboard statistics and data for personal dashboard
     */
    public function getDashboardData(DashboardRequest $request): JsonResponse
    {
        try {
            $data = $this->dashboardService->getDashboardStats();
            $resource = new DashboardResource($data);
            return $this->successResponse($resource->toArray($request), 'Dashboard data retrieved successfully');
        } catch (\Exception $e) {
            return $this->errorResponse(null, 'Failed to retrieve dashboard data: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get only statistics data (without charts)
     */
    public function getStats(): JsonResponse
    {
        try {
            $data = $this->dashboardService->getDashboardStats();
            return $this->successResponse($data['stats'], 'Dashboard statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->errorResponse(null, 'Failed to retrieve dashboard statistics: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get only charts data (without stats)
     */
    public function getCharts(): JsonResponse
    {
        try {
            $data = $this->dashboardService->getDashboardStats();
            return $this->successResponse($data['charts'], 'Dashboard charts data retrieved successfully');
        } catch (\Exception $e) {
            return $this->errorResponse(null, 'Failed to retrieve dashboard charts: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get summary statistics for quick overview
     */
    public function getSummary(): JsonResponse
    {
        try {
            $data = $this->dashboardService->getDashboardStats();
            $summary = [
                'totalBots' => $data['stats']['chatbot']['total'],
                'totalConversations' => $data['stats']['conversation']['total'],
                'totalTokens' => $data['stats']['token']['total'],
                'totalDocuments' => $data['stats']['knowledge']['totalDocuments'],
                'storageUsedPercent' => round(($data['stats']['storage']['totalUsedMB'] / $data['stats']['storage']['quotaLimitMB']) * 100, 2)
            ];
            return $this->successResponse($summary, 'Dashboard summary retrieved successfully');
        } catch (\Exception $e) {
            return $this->errorResponse(null, 'Failed to retrieve dashboard summary: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('dashboard::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request) {}

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('dashboard::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('dashboard::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id) {}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id) {}
}
